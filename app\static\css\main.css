@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  /* 这里可以添加自定义组件样式 */
}

/* 全局表格样式优化 - 专业版 */

/* 表头样式 */
.table th,
.table thead th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th,
table thead th,
table th {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    color: #6c757d !important;
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

/* 表格内容样式 - 全部使用普通字体 */
.table td,
.table tbody td,
.table-sm td,
.table-sm tbody td,
.table-bordered td,
.table-striped td,
.table-hover td,
table td,
table tbody td {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    border-top: 1px solid #e9ecef !important;
}

/* 表格内的链接也使用普通字体 */
.table td a,
.table tbody td a,
.table-sm td a,
.table-sm tbody td a,
table td a,
table tbody td a {
    font-weight: normal !important;
    text-decoration: none;
}

/* 表格内的强调文本也使用普通字体 */
.table td strong,
.table td b,
.table tbody td strong,
.table tbody td b,
.table-sm td strong,
.table-sm td b,
table td strong,
table td b {
    font-weight: 500 !important; /* 稍微加粗但不是黑体 */
}

/* 深色表头保持原有颜色但使用普通字体 */
.thead-dark th {
    background-color: #343a40 !important;
    color: #fff !important;
    border-color: #454d55 !important;
    font-weight: normal !important;
}

/* 表格行样式优化 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 数字列专用样式 */
.table .number-column,
.table .amount-column {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-weight: normal !important;
    text-align: right;
}

/* 状态列样式 */
.table .status-column {
    font-weight: normal !important;
}

/* 全局强制普通字体样式 - 覆盖所有可能的黑体 */
.table *,
.table-sm *,
.table-bordered *,
.table-striped *,
.table-hover *,
table * {
    font-weight: normal !important;
}

/* 特殊情况：稍微加粗但不是黑体 */
.table .semi-bold,
.table .medium-weight {
    font-weight: 500 !important;
}

/* Bootstrap徽章样式覆盖 */
.badge,
.badge-primary,
.badge-secondary,
.badge-success,
.badge-danger,
.badge-warning,
.badge-info,
.badge-light,
.badge-dark {
    font-weight: normal !important;
}

/* 按钮样式覆盖 */
.btn,
.btn-primary,
.btn-secondary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-light,
.btn-dark,
.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-danger,
.btn-outline-warning,
.btn-outline-info,
.btn-outline-light,
.btn-outline-dark {
    font-weight: normal !important;
}

/* 链接样式覆盖 */
a, a:hover, a:focus, a:active {
    font-weight: normal !important;
}

/* 表单控件样式覆盖 */
.form-control,
.form-select,
.form-check-label,
.form-label,
label {
    font-weight: normal !important;
}

/* ========== 采购订单模块专用样式 ========== */

/* 采购订单状态样式 */
.purchase-order-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: normal !important;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
}

.purchase-order-status.status-pending,
.purchase-order-status.status-待确认 {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.purchase-order-status.status-confirmed,
.purchase-order-status.status-已确认 {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.purchase-order-status.status-delivered,
.purchase-order-status.status-准备入库 {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.purchase-order-status.status-cancelled,
.purchase-order-status.status-已取消 {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 采购订单操作按钮组 */
.purchase-order-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

.purchase-order-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: normal !important;
    line-height: 1.2;
    border-radius: 0.2rem;
    transition: all 0.15s ease-in-out;
}

.purchase-order-actions .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 采购订单流程步骤 - 紧凑水平进度条 */
.purchase-order-process {
    position: relative;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    margin: 1rem 0;
}

.purchase-order-process-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.purchase-order-process-title {
    font-size: 1rem;
    font-weight: 500 !important;
    color: #495057;
    margin: 0;
}

.purchase-order-process-progress {
    font-size: 0.875rem;
    color: #6c757d;
}

.purchase-order-process-bar {
    position: relative;
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    margin: 1rem 0;
    overflow: hidden;
}

.purchase-order-process-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    border-radius: 2px;
    transition: width 0.8s ease-in-out;
    position: relative;
}

.purchase-order-process-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.purchase-order-process-steps {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    margin-top: 1rem;
}

.purchase-order-process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0 0.5rem;
}

.purchase-order-process-step-icon {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.purchase-order-process-step.pending .purchase-order-process-step-icon {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.purchase-order-process-step.active .purchase-order-process-step-icon {
    background-color: #ffc107;
    color: #212529;
    border: 2px solid #ffc107;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
}

.purchase-order-process-step.completed .purchase-order-process-step-icon {
    background-color: #28a745;
    color: #fff;
    border: 2px solid #28a745;
}

.purchase-order-process-step-title {
    font-size: 0.75rem;
    font-weight: 500 !important;
    color: #495057;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.purchase-order-process-step.active .purchase-order-process-step-title {
    color: #ffc107;
    font-weight: 600 !important;
}

.purchase-order-process-step.completed .purchase-order-process-step-title {
    color: #28a745;
}

.purchase-order-process-step-time {
    font-size: 0.625rem;
    color: #6c757d;
    font-weight: normal !important;
    line-height: 1.2;
}

.purchase-order-process-step:hover .purchase-order-process-step-icon {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 当前状态指示器 */
.purchase-order-current-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    background-color: #fff;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
    margin-top: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.purchase-order-current-status-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.75rem;
}

.purchase-order-current-status-text {
    font-size: 0.875rem;
    font-weight: 500 !important;
    color: #495057;
}

.purchase-order-current-status.status-pending .purchase-order-current-status-icon {
    background-color: #fff3cd;
    color: #856404;
}

.purchase-order-current-status.status-active .purchase-order-current-status-icon {
    background-color: #d1ecf1;
    color: #0c5460;
}

.purchase-order-current-status.status-completed .purchase-order-current-status-icon {
    background-color: #d4edda;
    color: #155724;
}

/* 快捷操作区域 */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.25rem;
}

.quick-action-item .btn {
    min-width: 120px;
    font-weight: 500 !important;
}

.quick-action-item small {
    color: #6c757d;
    font-size: 0.7rem;
}

/* 采购订单详情卡片 */
.purchase-order-detail-card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1rem;
    transition: box-shadow 0.15s ease-in-out;
}

.purchase-order-detail-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.purchase-order-detail-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-weight: normal !important;
}

.purchase-order-detail-card .card-body {
    padding: 1rem;
}

/* 采购订单金额显示 */
.purchase-order-amount {
    font-size: 1.1rem;
    font-weight: 500 !important;
    color: #28a745;
}

.purchase-order-amount-large {
    font-size: 1.5rem;
    font-weight: 600 !important;
    color: #28a745;
}

/* 采购订单时间线 */
.purchase-order-timeline {
    position: relative;
    padding-left: 2rem;
}

.purchase-order-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.purchase-order-timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.purchase-order-timeline-item::before {
    content: '';
    position: absolute;
    left: -2.25rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #28a745;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.purchase-order-timeline-item.pending::before {
    background-color: #6c757d;
}

.purchase-order-timeline-item.active::before {
    background-color: #ffc107;
}

.purchase-order-timeline-title {
    font-weight: 500 !important;
    color: #495057;
    margin-bottom: 0.25rem;
}

.purchase-order-timeline-time {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: normal !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .purchase-order-process {
        padding: 1rem 0.75rem;
    }

    .purchase-order-process-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .purchase-order-process-steps {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .purchase-order-process-step {
        flex: 0 0 calc(50% - 0.375rem);
        padding: 0.5rem;
        background-color: rgba(255,255,255,0.7);
        border-radius: 0.375rem;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .purchase-order-process-step-title {
        font-size: 0.7rem;
    }

    .purchase-order-process-step-time {
        font-size: 0.6rem;
    }

    .purchase-order-current-status {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .purchase-order-current-status-icon {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .purchase-order-actions {
        justify-content: center;
    }

    .purchase-order-actions .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

@media (max-width: 480px) {
    .purchase-order-process-step {
        flex: 0 0 100%;
    }

    .purchase-order-process-steps {
        flex-direction: column;
        gap: 0.5rem;
    }

    .purchase-order-process-step {
        flex-direction: row;
        text-align: left;
        padding: 0.75rem;
    }

    .purchase-order-process-step-icon {
        margin-right: 0.75rem;
        margin-bottom: 0;
    }

    .purchase-order-process-step-content {
        flex: 1;
    }
}
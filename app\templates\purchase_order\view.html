{% extends 'base.html' %}

{% block title %}采购订单详情 - {{ order.order_number }} - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
.order-status {
    font-size: 0.85em;
    padding: 0.25em 0.6em;
    border-radius: 0.25rem;
}
.status-pending {
    background-color: #ffc107;
    color: #000;
}
.status-confirmed {
    background-color: #17a2b8;
    color: #fff;
}
.status-delivered {
    background-color: #28a745;
    color: #fff;
}
.status-cancelled {
    background-color: #dc3545;
    color: #fff;
}
.timeline {
    position: relative;
    padding: 20px 0;
}
.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background: #e9ecef;
}
.timeline-item {
    position: relative;
    padding-left: 50px;
    margin-bottom: 20px;
}
.timeline-item::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
}
.timeline-item.active::before {
    background: #28a745;
}
.timeline-content {
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 4px;
}
.timeline-time {
    font-size: 0.85em;
    color: #6c757d;
}
/* 按钮状态样式 */
.btn-action-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}
.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}
.next-step-guide {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #e8f4ff;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}
.process-flow {
    display: flex;
    align-items: center;
    margin-top: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
}
.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    min-width: 120px;
    text-align: center;
}
.process-step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}
.process-step-active .process-step-icon {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}
.process-step-completed .process-step-icon {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}
.process-connector {
    width: 30px;
    height: 2px;
    background-color: #dee2e6;
    margin: 0 5px;
}
.process-step-completed + .process-connector,
.process-step-active + .process-connector {
    background-color: #007bff;
}

/* 状态信息样式 */
.status-info-column {
    min-width: 200px;
}

.status-info-column .badge {
    display: block;
    margin-bottom: 2px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 消耗状态行背景色 */
.table-light {
    background-color: #f8f9fa !important;
}

.table-warning {
    background-color: #fff3cd !important;
}

/* 状态徽章样式优化 */
.badge-consumed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.badge-partial-consumed {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.badge-not-consumed {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

/* 消耗餐次详情样式 */
.consumption-details {
    font-size: 0.7em;
    line-height: 1.2;
    color: #6c757d;
}

.consumption-details .meal-item {
    display: inline-block;
    margin-right: 8px;
    padding: 1px 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    margin-bottom: 2px;
}

/* 紧凑流程样式 */
.compact-process-flow {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    padding: 15px 0;
}

.compact-process-step {
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;
}

.compact-process-step.completed {
    color: #28a745;
}

.compact-process-step.active {
    color: #007bff;
    transform: scale(1.02);
}

.compact-process-step.pending {
    color: #6c757d;
    opacity: 0.7;
}

.compact-step-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-size: 20px;
    border: 2px solid;
    transition: all 0.2s ease;
}

.compact-process-step.completed .compact-step-icon {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.compact-process-step.active .compact-step-icon {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.compact-process-step.pending .compact-step-icon {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.compact-step-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 2px;
    line-height: 1.2;
}

.compact-step-time {
    font-size: 0.7rem;
    color: #6c757d;
    line-height: 1;
}

.compact-process-step.completed .compact-step-time {
    color: #28a745;
}

.compact-process-step.active .compact-step-time {
    color: #007bff;
}

/* 快捷操作区域 */
.quick-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.quick-action-item {
    text-align: center;
}

.quick-action-item small {
    font-size: 0.75em;
    margin-top: 5px;
    display: block;
    color: #6c757d;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .compact-process-flow {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .compact-step-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .compact-step-title {
        font-size: 0.8rem;
    }

    .compact-step-time {
        font-size: 0.65rem;
    }
}

/* 订单状态卡片 */
.order-status-card .card {
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.border-left-primary {
    border-left-color: #007bff !important;
}


</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>采购订单详情</h2>
            <p class="text-muted">订单号：{{ order.order_number }}</p>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-action-group">
                <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}"
                   class="btn btn-outline-primary"
                   target="_blank">
                    <i class="fas fa-print"></i> 打印订单
                </a>

                <!-- 管理员操作下拉菜单 -->
                {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-cog"></i> 管理操作
                    </button>
                    <div class="dropdown-menu dropdown-menu-right">
                        {% if not order.has_stock_in %}
                        <button class="dropdown-item delete-btn" data-id="{{ order.id }}">
                            <i class="fas fa-trash-alt text-danger"></i> 删除订单
                        </button>
                        {% endif %}
                        <div class="dropdown-divider"></div>
                        <h6 class="dropdown-header">订单状态</h6>
                        <span class="dropdown-item-text">
                            <small class="text-muted">当前状态：{{ order.status_display }}</small>
                        </span>
                    </div>
                </div>
                {% endif %}
            </div>

        </div>
    </div>

    <!-- 订单处理流程 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">订单处理流程</h5>
                    <small class="text-muted">点击步骤可执行相应操作</small>
                </div>
                <div class="card-body">
                    <div class="purchase-order-process">
                        <!-- 进度条头部 -->
                        <div class="purchase-order-process-header">
                            <div class="purchase-order-process-title">订单处理进度</div>
                            <div class="purchase-order-process-progress">
                                {% set total_steps = 5 %}
                                {% set completed_steps = 1 %}
                                {% if order.status in ['已确认', '准备入库'] or order.has_stock_in %}
                                    {% set completed_steps = 2 %}
                                {% endif %}
                                {% if order.status in ['准备入库'] or order.has_stock_in %}
                                    {% set completed_steps = 3 %}
                                {% endif %}
                                {% if order.has_stock_in %}
                                    {% set completed_steps = 4 %}
                                {% endif %}
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    {% set completed_steps = 5 %}
                                {% endif %}
                                {{ completed_steps }}/{{ total_steps }}
                            </div>
                        </div>

                        <!-- 进度条 -->
                        <div class="purchase-order-process-bar">
                            <div class="purchase-order-process-bar-fill" style="width: {{ (completed_steps / total_steps * 100)|round(1) }}%"></div>
                        </div>

                        <!-- 步骤图标 -->
                        <div class="purchase-order-process-steps">
                            <!-- 订单创建 -->
                            <div class="purchase-order-process-step completed">
                                <div class="purchase-order-process-step-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="purchase-order-process-step-content">
                                    <div class="purchase-order-process-step-title">订单创建</div>
                                    <div class="purchase-order-process-step-time">{{ order.order_date|format_datetime('%m-%d') if order.order_date else '-' }}</div>
                                </div>
                            </div>

                            <!-- 订单确认 -->
                            <div class="purchase-order-process-step {% if order.status in ['已确认', '准备入库'] or order.has_stock_in %}completed{% elif order.status == '待确认' %}active{% else %}pending{% endif %}"
                                 {% if order.status == '待确认' and not order.has_stock_in %}data-action="confirm"{% endif %}>
                                <div class="purchase-order-process-step-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="purchase-order-process-step-content">
                                    <div class="purchase-order-process-step-title">订单确认</div>
                                    <div class="purchase-order-process-step-time">
                                        {% if order.confirmed_at %}
                                            {{ order.confirmed_at|format_datetime('%m-%d') }}
                                        {% elif order.status == '待确认' %}
                                            进行中
                                        {% else %}
                                            待处理
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- 订单送达 -->
                            <div class="purchase-order-process-step {% if order.status in ['准备入库'] or order.has_stock_in %}completed{% elif order.status == '已确认' %}active{% else %}pending{% endif %}"
                                 {% if order.status == '已确认' and not order.has_stock_in %}data-action="deliver"{% endif %}>
                                <div class="purchase-order-process-step-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="purchase-order-process-step-content">
                                    <div class="purchase-order-process-step-title">订单送达</div>
                                    <div class="purchase-order-process-step-time">
                                        {% if order.delivered_at %}
                                            {{ order.delivered_at|format_datetime('%m-%d') }}
                                        {% elif order.status == '已确认' %}
                                            进行中
                                        {% else %}
                                            待处理
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- 创建入库单 -->
                            <div class="purchase-order-process-step {% if order.has_stock_in %}completed{% elif order.status == '准备入库' %}active{% else %}pending{% endif %}"
                                 {% if order.status == '准备入库' and not order.has_stock_in %}data-action="stock-in"{% endif %}>
                                <div class="purchase-order-process-step-icon">
                                    <i class="fas fa-dolly"></i>
                                </div>
                                <div class="purchase-order-process-step-content">
                                    <div class="purchase-order-process-step-title">创建入库单</div>
                                    <div class="purchase-order-process-step-time">
                                        {% if order.has_stock_in %}
                                            已完成
                                        {% elif order.status == '准备入库' %}
                                            进行中
                                        {% else %}
                                            待处理
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- 完成入库 -->
                            <div class="purchase-order-process-step {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}completed{% elif order.has_stock_in %}active{% else %}pending{% endif %}">
                                <div class="purchase-order-process-step-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="purchase-order-process-step-content">
                                    <div class="purchase-order-process-step-title">完成入库</div>
                                    <div class="purchase-order-process-step-time">
                                        {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                            {{ order.active_stock_in.updated_at|format_datetime('%m-%d') if order.active_stock_in.updated_at else '-' }}
                                        {% elif order.has_stock_in %}
                                            进行中
                                        {% else %}
                                            待处理
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 当前状态指示器 -->
                        <div class="purchase-order-current-status {% if order.status == '待确认' %}status-pending{% elif order.status in ['已确认', '准备入库'] %}status-active{% elif order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}status-completed{% else %}status-pending{% endif %}">
                            <div class="purchase-order-current-status-icon">
                                {% if order.status == '待确认' %}
                                    <i class="fas fa-clock"></i>
                                {% elif order.status == '已确认' %}
                                    <i class="fas fa-truck"></i>
                                {% elif order.status == '准备入库' %}
                                    <i class="fas fa-dolly"></i>
                                {% elif order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    <i class="fas fa-check-circle"></i>
                                {% else %}
                                    <i class="fas fa-clock"></i>
                                {% endif %}
                            </div>
                            <div class="purchase-order-current-status-text">
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    订单流程已完成，食材已入库
                                {% elif order.status == '已取消' %}
                                    订单已取消
                                {% elif order.status == '准备入库' %}
                                    食材已送达，等待创建入库单
                                {% elif order.status == '已确认' %}
                                    订单已确认，等待送达
                                {% else %}
                                    订单等待确认
                                {% endif %}
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <!-- 订单状态卡片 -->
        <div class="col-lg-4 d-flex align-items-end">
            <div class="order-status-card w-100">
                <div class="card border-left-primary">
                    <div class="card-body py-3">
                        <div class="d-flex align-items-center">
                            <div class="status-icon mr-3">
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    <i class="fas fa-check-circle text-success fa-2x"></i>
                                {% elif order.status == '已取消' %}
                                    <i class="fas fa-times-circle text-danger fa-2x"></i>
                                {% elif order.status == '准备入库' %}
                                    <i class="fas fa-dolly text-primary fa-2x"></i>
                                {% elif order.status == '已确认' %}
                                    <i class="fas fa-truck text-info fa-2x"></i>
                                {% else %}
                                    <i class="fas fa-clock text-warning fa-2x"></i>
                                {% endif %}
                            </div>
                            <div class="status-info">
                                <h6 class="mb-1">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        已完成入库
                                    {% else %}
                                        {{ order.status_display }}
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        订单流程已完成，食材已入库
                                    {% elif order.status == '已取消' %}
                                        订单已取消
                                    {% elif order.status == '准备入库' %}
                                        食材已送达，等待创建入库单
                                    {% elif order.status == '已确认' %}
                                        订单已确认，等待送达
                                    {% else %}
                                        订单等待确认
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- 状态相关的徽章 -->
                        <div class="mt-2">
                            {% if order.status_info %}
                                {% if order.status_info.has_stock_in and order.active_stock_in %}
                                    <span class="badge badge-info">{{ order.active_stock_in.status }}</span>
                                {% else %}
                                    <span class="badge badge-secondary">未入库</span>
                                {% endif %}

                                {% if order.status_info.consumption_status != '未消耗' %}
                                    <span class="badge {% if order.status_info.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %}">
                                        {{ order.status_info.consumption_status }}
                                    </span>
                                {% endif %}
                            {% endif %}
                        </div>

                        <!-- 管理操作按钮 - 突出显示 -->
                        {% if order.has_stock_in and order.active_stock_in %}
                        <div class="mt-3">
                            <a href="{{ url_for('stock_in.view_details', id=order.active_stock_in.id) }}"
                               class="btn btn-info btn-block">
                                <i class="fas fa-eye"></i> 查看入库单
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 订单信息 -->
        <div class="col-md-8">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>订单编号：</strong>{{ order.order_number }}</p>
                            <p><strong>创建时间：</strong>{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</p>
                            <p>
                                <strong>订单状态：</strong>
                                {% if order.status == '待确认' %}
                                <span class="purchase-order-status status-pending">待确认</span>
                                {% elif order.status == '已确认' %}
                                <span class="purchase-order-status status-confirmed">已确认</span>
                                {% elif order.status == '准备入库' %}
                                <span class="purchase-order-status status-delivered">准备入库</span>
                                {% elif order.status == '已取消' %}
                                <span class="purchase-order-status status-cancelled">已取消</span>
                                {% else %}
                                <span class="purchase-order-status">{{ order.get_status_display() }}</span>
                                {% endif %}

                                {% if order.status_info %}
                                    <!-- 入库状态 -->
                                    {% if order.status_info.has_stock_in %}
                                        <span class="badge badge-info ml-2">{{ order.status_info.stock_in_status }}</span>
                                    {% else %}
                                        <span class="badge badge-secondary ml-2">未入库</span>
                                    {% endif %}

                                    <!-- 消耗状态 -->
                                    {% if order.status_info.consumption_status != '未消耗' %}
                                        <span class="badge {% if order.status_info.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %} ml-2">
                                            {{ order.status_info.consumption_status }}
                                        </span>
                                    {% endif %}
                                {% endif %}
                            </p>

                            <!-- 消耗详情 -->
                            {% if order.status_info and order.status_info.consumption_details %}
                            <p>
                                <strong>消耗餐次：</strong>
                                <span class="text-muted">
                                    {% for detail in order.status_info.consumption_details %}
                                        {{ detail.consumption_date|format_datetime('%Y-%m-%d') }} {{ detail.meal_type }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </span>
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <p><strong>采购区域：</strong>{{ order.area_name if order.area_name else '-' }}</p>
                            <p><strong>预计送货日期：</strong>{{ order.expected_delivery_date|format_datetime('%Y-%m-%d') if order.expected_delivery_date else '-' }}</p>
                            <p><strong>实际送货日期：</strong>{{ order.delivery_date|format_datetime('%Y-%m-%d') if order.delivery_date else '-' }}</p>
                        </div>
                    </div>
                    {% if order.notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>备注：</strong>{{ order.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">采购明细</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th style="width: 5%">序号</th>
                                    <th class="w-25">食材名称</th>
                                    <th style="width: 12%">采购量</th>
                                    <th style="width: 8%">单位</th>
                                    <th class="w-20">供应商</th>
                                    <th class="w-30">状态信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order['order_items'] %}
                                <tr class="{% if order.status_info and order.status_info.items_status %}
                                    {% for item_status in order.status_info.items_status %}
                                        {% if item_status.ingredient_name == item.ingredient_name %}
                                            {% if item_status.consumption_status == '已消耗' %}table-light{% elif item_status.consumption_status == '部分消耗' %}table-warning{% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}">
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.ingredient_name }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit }}</td>
                                    <td>{{ item.supplier_name if item.supplier_name else '自购' }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <!-- 入库状态 -->
                                            {% if order.status_info and order.status_info.has_stock_in %}
                                                <span class="badge badge-info mb-1" style="font-size: 0.75em;">
                                                    已入库 ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                </span>
                                            {% else %}
                                                <span class="badge badge-secondary mb-1" style="font-size: 0.75em;">未入库</span>
                                            {% endif %}

                                            <!-- 消耗状态 -->
                                            {% if order.status_info and order.status_info.items_status %}
                                                {% for item_status in order.status_info.items_status %}
                                                    {% if item_status.ingredient_name == item.ingredient_name %}
                                                        {% if item_status.consumption_status != '未消耗' %}
                                                            <span class="badge {% if item_status.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %} mb-1" style="font-size: 0.75em;">
                                                                {{ item_status.consumption_status }}
                                                            </span>

                                                            <!-- 消耗餐次详情 -->
                                                            {% if item_status.consumption_meals %}
                                                                <small class="text-muted" style="font-size: 0.7em;">
                                                                    {% for meal in item_status.consumption_meals %}
                                                                        {{ meal.consumption_date|format_datetime('%m-%d') }} {{ meal.meal_type }}{% if not loop.last %}<br>{% endif %}
                                                                    {% endfor %}
                                                                </small>
                                                            {% endif %}
                                                        {% else %}
                                                            <span class="badge badge-light mb-1" style="font-size: 0.75em;">未消耗</span>
                                                        {% endif %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单状态时间线 -->
        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单状态时间线</h5>
                </div>
                <div class="card-body">
                    <div class="purchase-order-timeline">
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">订单创建</div>
                            <div class="purchase-order-timeline-time">{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</div>
                        </div>
                        {% if order.confirmed_at %}
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">订单确认</div>
                            <div class="purchase-order-timeline-time">{{ order.confirmed_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                        {% endif %}
                        {% if order.delivered_at %}
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">已送达</div>
                            <div class="purchase-order-timeline-time">{{ order.delivered_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            {% if order.delivery_notes %}
                            <div class="text-muted small">备注：{{ order.delivery_notes }}</div>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% if order.cancelled_at %}
                        <div class="purchase-order-timeline-item pending">
                            <div class="purchase-order-timeline-title">已取消</div>
                            <div class="purchase-order-timeline-time">{{ order.cancelled_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            {% if order.cancel_reason %}
                            <div class="text-muted small">原因：{{ order.cancel_reason }}</div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？标记送达后，订单将进入"准备入库"状态，您可以创建入库单。</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 采购订单流程步骤点击事件
    $('.purchase-order-process-step[data-action]').click(function() {
        const action = $(this).data('action');

        if (action === 'confirm') {
            $('#confirmModal').modal('show');
        } else if (action === 'deliver') {
            $('#deliverModal').modal('show');
        } else if (action === 'stock-in') {
            window.location.href = '{{ url_for("stock_in.create_from_purchase_order", order_id=order.id) }}';
        }
    });

    // 确认订单
    $('.confirm-btn').click(function() {
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.confirm_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('确认订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
                $('#confirmModal').modal('hide');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            alert('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.cancel_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('取消订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
                $('#cancelModal').modal('hide');
            }
        });
    });

    // 标记送达
    $('.deliver-btn, .purchase-order-process-step[data-action="deliver"]').click(function() {
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.deliver_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('标记送达失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
                $('#deliverModal').modal('hide');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.delete_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 删除成功后跳转到列表页
                    window.location.href = '{{ url_for("purchase_order.index") }}';
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('删除订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });
});
</script>
{% endblock %}
